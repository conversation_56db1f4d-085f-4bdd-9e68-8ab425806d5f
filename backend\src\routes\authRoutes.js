/**
 * Authentication Routes
 * 
 * This module defines all HTTP routes for authentication operations.
 * Routes include login, registration, profile management, and password operations.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import express from 'express';
import {
  login,
  register,
  getProfile,
  updateProfile,
  changePassword,
  logout
} from '../controllers/authController.js';
import { authenticateToken, refreshToken } from '../middlewares/auth.js';

const router = express.Router();

/**
 * @route   POST /api/auth/login
 * @desc    Authenticate user and return JWT token
 * @access  Public
 * @body    Login credentials:
 *          - email: User email (required)
 *          - password: User password (required)
 */
router.post('/login', login);

/**
 * @route   POST /api/auth/register
 * @desc    Register a new employee account
 * @access  Public (should be restricted in production)
 * @body    Employee registration data:
 *          - firstName: First name (required)
 *          - lastName: Last name (required)
 *          - email: Email address (required, unique)
 *          - password: Password (required, min 6 chars)
 *          - position: Job position (required)
 *          - department: Department ID (required)
 *          - phone: Phone number
 *          - salary: Salary amount
 */
router.post('/register', register);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh JWT token using refresh token
 * @access  Public
 * @body    Refresh token data:
 *          - refreshToken: Valid refresh token (required)
 */
router.post('/refresh', refreshToken);

/**
 * @route   GET /api/auth/profile
 * @desc    Get current user's profile information
 * @access  Private (requires authentication)
 */
router.get('/profile', authenticateToken, getProfile);

/**
 * @route   PUT /api/auth/profile
 * @desc    Update current user's profile information
 * @access  Private (requires authentication)
 * @body    Profile update data:
 *          - firstName: First name
 *          - lastName: Last name
 *          - phone: Phone number
 *          - address: Address object
 *          - emergencyContact: Emergency contact object
 *          - skills: Array of skills
 *          - bio: Biography text
 *          Note: Sensitive fields like password, role, status cannot be updated here
 */
router.put('/profile', authenticateToken, updateProfile);

/**
 * @route   POST /api/auth/change-password
 * @desc    Change user's password
 * @access  Private (requires authentication)
 * @body    Password change data:
 *          - currentPassword: Current password (required)
 *          - newPassword: New password (required, min 6 chars)
 */
router.post('/change-password', authenticateToken, changePassword);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user (invalidate session)
 * @access  Private (requires authentication)
 */
router.post('/logout', authenticateToken, logout);

export default router;
