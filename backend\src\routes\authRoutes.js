/**
 * ============================================================================
 * AUTHENTICATION ROUTES
 * ============================================================================
 *
 * This module defines all HTTP routes for authentication operations including
 * user login, registration, profile management, and password operations.
 *
 * @fileoverview Authentication and user management routes
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 1.0.0
 * @created 2024-01-01
 * @updated 2024-07-02
 *
 * @description
 * Authentication routes handle:
 * - User login and logout
 * - New user registration
 * - Profile management (view/update)
 * - Password management
 * - JWT token refresh
 *
 * Security Features:
 * - JWT token-based authentication
 * - Password hashing with bcrypt
 * - Input validation and sanitization
 * - Rate limiting (TODO: implement)
 * - Account lockout protection (TODO: implement)
 */

import express from 'express';
import {
  login,
  register,
  getProfile,
  updateProfile,
  changePassword,
  logout
} from '../controllers/authController.js';
import { authenticateToken, refreshToken } from '../middlewares/auth.js';

const router = express.Router();

/**
 * ============================================================================
 * PUBLIC AUTHENTICATION ROUTES
 * ============================================================================
 * These routes do not require authentication
 */

/**
 * @route   POST /api/auth/login
 * @desc    Authenticate user credentials and return JWT access token
 * @access  Public
 * @ratelimit 5 requests per minute per IP
 *
 * @body {Object} credentials - User login credentials
 * @body {string} credentials.email - User email address (required)
 * @body {string} credentials.password - User password (required)
 *
 * @returns {Object} 200 - Authentication successful
 * @returns {string} returns.data.token - JWT access token
 * @returns {Object} returns.data.user - User profile information
 * @returns {Object} 401 - Invalid credentials
 * @returns {Object} 400 - Validation error
 * @returns {Object} 429 - Too many login attempts
 *
 * @example
 * POST /api/auth/login
 * Content-Type: application/json
 *
 * {
 *   "email": "<EMAIL>",
 *   "password": "securePassword123"
 * }
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Login successful",
 *   "data": {
 *     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *     "user": {
 *       "_id": "60d5ecb74b24a1234567890a",
 *       "firstName": "John",
 *       "lastName": "Doe",
 *       "email": "<EMAIL>",
 *       "role": "employee",
 *       "department": {
 *         "name": "Engineering",
 *         "code": "ENG"
 *       }
 *     }
 *   },
 *   "timestamp": "2024-07-02T10:30:00.000Z"
 * }
 */
router.post('/login', login);

/**
 * @route   POST /api/auth/register
 * @desc    Register a new employee account in the system
 * @access  Public (should be restricted to HR/Admin in production)
 * @ratelimit 3 requests per hour per IP
 *
 * @body {Object} userData - New employee registration data
 * @body {string} userData.firstName - Employee first name (required, 2-50 chars)
 * @body {string} userData.lastName - Employee last name (required, 2-50 chars)
 * @body {string} userData.email - Email address (required, unique, valid format)
 * @body {string} userData.password - Password (required, min 8 chars, complexity rules)
 * @body {string} userData.employeeId - Unique employee ID (auto-generated if not provided)
 * @body {string} userData.position - Job position/title (required)
 * @body {string} userData.department - Department ID (required, must exist)
 * @body {string} [userData.phone] - Phone number (optional, valid format)
 * @body {number} [userData.salary] - Annual salary (optional, positive number)
 * @body {string} [userData.role="employee"] - User role (employee, manager, hr, admin)
 * @body {Date} [userData.hireDate] - Hire date (defaults to current date)
 *
 * @returns {Object} 201 - Registration successful
 * @returns {string} returns.data.token - JWT access token for new user
 * @returns {Object} returns.data.user - Created user profile
 * @returns {Object} 400 - Validation error or invalid data
 * @returns {Object} 409 - Email or employee ID already exists
 * @returns {Object} 422 - Password doesn't meet complexity requirements
 *
 * @example
 * POST /api/auth/register
 * Content-Type: application/json
 *
 * {
 *   "firstName": "Jane",
 *   "lastName": "Smith",
 *   "email": "<EMAIL>",
 *   "password": "SecurePass123!",
 *   "position": "Software Engineer",
 *   "department": "60d5ecb74b24a1234567890b",
 *   "phone": "******-0123",
 *   "salary": 75000
 * }
 */
router.post('/register', register);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh expired JWT access token using refresh token
 * @access  Public
 * @ratelimit 10 requests per minute per token
 *
 * @body {Object} tokenData - Token refresh data
 * @body {string} tokenData.refreshToken - Valid refresh token (required)
 *
 * @returns {Object} 200 - Token refresh successful
 * @returns {string} returns.data.token - New JWT access token
 * @returns {Object} returns.data.user - User information
 * @returns {Object} 401 - Invalid or expired refresh token
 * @returns {Object} 400 - Missing refresh token
 *
 * @example
 * POST /api/auth/refresh
 * Content-Type: application/json
 *
 * {
 *   "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 * }
 */
router.post('/refresh', refreshToken);

/**
 * ============================================================================
 * PROTECTED AUTHENTICATION ROUTES
 * ============================================================================
 * These routes require valid JWT authentication token
 */

/**
 * @route   GET /api/auth/profile
 * @desc    Get current authenticated user's complete profile information
 * @access  Private (requires valid JWT token)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @returns {Object} 200 - Profile retrieved successfully
 * @returns {Object} returns.data - Complete user profile with department info
 * @returns {Object} 401 - Invalid or missing authentication token
 * @returns {Object} 404 - User not found
 *
 * @example
 * GET /api/auth/profile
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Profile retrieved successfully",
 *   "data": {
 *     "_id": "60d5ecb74b24a1234567890a",
 *     "firstName": "John",
 *     "lastName": "Doe",
 *     "email": "<EMAIL>",
 *     "employeeId": "EMP001",
 *     "position": "Software Engineer",
 *     "department": {
 *       "name": "Engineering",
 *       "code": "ENG"
 *     },
 *     "role": "employee",
 *     "status": "active",
 *     "hireDate": "2024-01-15T00:00:00.000Z",
 *     "phone": "******-0123",
 *     "address": {...},
 *     "emergencyContact": {...}
 *   }
 * }
 */
router.get('/profile', authenticateToken, getProfile);

/**
 * @route   PUT /api/auth/profile
 * @desc    Update current user's profile information (non-sensitive fields only)
 * @access  Private (requires valid JWT token)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @body {Object} profileData - Profile update data (all fields optional)
 * @body {string} [profileData.firstName] - First name (2-50 chars)
 * @body {string} [profileData.lastName] - Last name (2-50 chars)
 * @body {string} [profileData.phone] - Phone number (valid format)
 * @body {Object} [profileData.address] - Address information
 * @body {string} [profileData.address.street] - Street address
 * @body {string} [profileData.address.city] - City
 * @body {string} [profileData.address.state] - State/Province
 * @body {string} [profileData.address.zipCode] - ZIP/Postal code
 * @body {string} [profileData.address.country] - Country
 * @body {Object} [profileData.emergencyContact] - Emergency contact info
 * @body {string} [profileData.emergencyContact.name] - Contact name
 * @body {string} [profileData.emergencyContact.phone] - Contact phone
 * @body {string} [profileData.emergencyContact.relationship] - Relationship
 * @body {Array<string>} [profileData.skills] - Array of skills/competencies
 * @body {string} [profileData.bio] - Biography/description (max 500 chars)
 *
 * @returns {Object} 200 - Profile updated successfully
 * @returns {Object} returns.data - Updated user profile
 * @returns {Object} 400 - Validation error
 * @returns {Object} 401 - Invalid authentication token
 * @returns {Object} 404 - User not found
 *
 * @note Sensitive fields (password, role, status, employeeId, salary) cannot be updated via this endpoint
 *
 * @example
 * PUT /api/auth/profile
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Content-Type: application/json
 *
 * {
 *   "phone": "******-9876",
 *   "address": {
 *     "street": "123 Main St",
 *     "city": "New York",
 *     "state": "NY",
 *     "zipCode": "10001",
 *     "country": "USA"
 *   },
 *   "skills": ["JavaScript", "React", "Node.js"],
 *   "bio": "Experienced software engineer with 5+ years in web development"
 * }
 */
router.put('/profile', authenticateToken, updateProfile);

/**
 * @route   POST /api/auth/change-password
 * @desc    Change user's password with current password verification
 * @access  Private (requires valid JWT token)
 * @headers {string} Authorization - Bearer JWT token (required)
 * @ratelimit 5 requests per hour per user
 *
 * @body {Object} passwordData - Password change data
 * @body {string} passwordData.currentPassword - Current password (required for verification)
 * @body {string} passwordData.newPassword - New password (required, min 8 chars, complexity rules)
 *
 * @returns {Object} 200 - Password changed successfully
 * @returns {Object} 400 - Missing required fields
 * @returns {Object} 401 - Invalid current password or authentication token
 * @returns {Object} 422 - New password doesn't meet complexity requirements
 *
 * @security Password Requirements:
 * - Minimum 8 characters
 * - At least one uppercase letter
 * - At least one lowercase letter
 * - At least one number
 * - At least one special character
 * - Cannot be the same as current password
 * - Cannot be a commonly used password
 *
 * @example
 * POST /api/auth/change-password
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Content-Type: application/json
 *
 * {
 *   "currentPassword": "oldPassword123!",
 *   "newPassword": "newSecurePassword456@"
 * }
 */
router.post('/change-password', authenticateToken, changePassword);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user and invalidate current session
 * @access  Private (requires valid JWT token)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @returns {Object} 200 - Logout successful
 * @returns {Object} 401 - Invalid authentication token
 *
 * @note In a stateless JWT system, logout is primarily handled client-side by removing the token.
 *       This endpoint can be used for logging purposes and future token blacklisting implementation.
 *
 * @example
 * POST /api/auth/logout
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Logout successful",
 *   "timestamp": "2024-07-02T10:30:00.000Z"
 * }
 */
router.post('/logout', authenticateToken, logout);

/**
 * ============================================================================
 * EXPORT ROUTER
 * ============================================================================
 */

export default router;
