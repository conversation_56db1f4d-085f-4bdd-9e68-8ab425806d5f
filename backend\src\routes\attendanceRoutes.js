/**
 * ============================================================================
 * ATTENDANCE MANAGEMENT ROUTES
 * ============================================================================
 *
 * This module defines all HTTP routes for attendance and time tracking operations
 * including clock in/out, break management, overtime tracking, and comprehensive
 * reporting and analytics.
 *
 * @fileoverview Attendance tracking and time management routes
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 1.0.0
 * @created 2024-01-01
 * @updated 2024-07-02
 *
 * @description
 * Attendance routes handle:
 * - Time tracking (clock in/out, breaks, overtime)
 * - Attendance record CRUD operations
 * - Monthly and yearly attendance reports
 * - Attendance statistics and analytics
 * - Leave management integration
 * - Compliance and audit reporting
 *
 * Security:
 * - All routes require authentication
 * - Employee-level access for own records
 * - Manager/HR access for team/company records
 * - Input validation and time zone handling
 */

import express from 'express';
import {
  getAllAttendance,
  getAttendanceById,
  createAttendance,
  updateAttendance,
  deleteAttendance,
  getAttendanceStats,
  getMonthlyAttendance
} from '../controllers/attendanceController.js';

const router = express.Router();

/**
 * ============================================================================
 * ATTENDANCE LISTING & ANALYTICS ROUTES
 * ============================================================================
 */

/**
 * @route   GET /api/attendance
 * @desc    Get paginated list of attendance records with comprehensive filtering
 * @access  Private (requires authentication, employees see own records, managers see team records)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @query {number} [page=1] - Page number for pagination (min: 1)
 * @query {number} [limit=10] - Number of items per page (min: 1, max: 100)
 * @query {string} [employee] - Filter by employee ID (managers/HR can specify, employees auto-filtered to self)
 * @query {string} [department] - Filter by department ID (HR/Admin only)
 * @query {string} [status] - Filter by attendance status (present, absent, late, half-day, overtime)
 * @query {string} [attendanceType] - Filter by type (regular, overtime, holiday, weekend, sick-leave, vacation)
 * @query {string} [workLocation] - Filter by work location (office, remote, client-site, travel)
 * @query {string} [dateFrom] - Start date for date range filter (ISO date string)
 * @query {string} [dateTo] - End date for date range filter (ISO date string)
 * @query {string} [sortBy=date] - Sort field (date, clockIn, clockOut, totalHours, status)
 * @query {string} [sortOrder=desc] - Sort direction (asc, desc)
 * @query {boolean} [includeBreaks=false] - Include break details in response
 * @query {boolean} [calculateTotals=true] - Include calculated totals (hours, overtime, etc.)
 *
 * @returns {Object} 200 - Attendance records retrieved successfully
 * @returns {Array} returns.data.records - Array of attendance record objects
 * @returns {Object} returns.data.pagination - Pagination metadata
 * @returns {Object} returns.data.summary - Summary statistics for the filtered records
 * @returns {Object} 400 - Invalid query parameters
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 *
 * @example
 * GET /api/attendance?employee=60d5ecb74b24a1234567890a&dateFrom=2024-07-01&dateTo=2024-07-31&includeBreaks=true
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Attendance records retrieved successfully",
 *   "data": {
 *     "records": [
 *       {
 *         "_id": "60d5ecb74b24a1234567890d",
 *         "employee": {
 *           "name": "John Doe",
 *           "employeeId": "EMP001"
 *         },
 *         "date": "2024-07-02T00:00:00.000Z",
 *         "clockIn": "2024-07-02T09:00:00.000Z",
 *         "clockOut": "2024-07-02T17:30:00.000Z",
 *         "totalHours": 8.5,
 *         "regularHours": 8,
 *         "overtimeHours": 0.5,
 *         "status": "present",
 *         "workLocation": "office",
 *         "breaks": [
 *           {
 *             "startTime": "2024-07-02T12:00:00.000Z",
 *             "endTime": "2024-07-02T13:00:00.000Z",
 *             "duration": 60,
 *             "type": "lunch"
 *           }
 *         ]
 *       }
 *     ],
 *     "pagination": {
 *       "currentPage": 1,
 *       "totalPages": 15,
 *       "totalItems": 147,
 *       "itemsPerPage": 10
 *     },
 *     "summary": {
 *       "totalDays": 22,
 *       "presentDays": 20,
 *       "absentDays": 2,
 *       "totalHours": 176,
 *       "averageHoursPerDay": 8.8
 *     }
 *   }
 * }
 */
router.get('/', getAllAttendance);

/**
 * @route   GET /api/attendance/stats
 * @desc    Get comprehensive attendance statistics and analytics
 * @access  Private (requires authentication, HR/Admin for company-wide stats)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @query {string} [period=month] - Statistics period (day, week, month, quarter, year)
 * @query {string} [department] - Filter by department ID (HR/Admin only)
 * @query {string} [employee] - Filter by employee ID (employees auto-filtered to self)
 * @query {string} [dateFrom] - Start date for custom period
 * @query {string} [dateTo] - End date for custom period
 *
 * @returns {Object} 200 - Statistics retrieved successfully
 * @returns {Object} returns.data.overview - General attendance statistics
 * @returns {Object} returns.data.trends - Attendance trends over time
 * @returns {Array} returns.data.departmentBreakdown - Statistics by department
 * @returns {Array} returns.data.statusDistribution - Distribution by attendance status
 * @returns {Object} returns.data.workPatterns - Work pattern analysis
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 *
 * @example
 * GET /api/attendance/stats?period=month&department=60d5ecb74b24a1234567890b
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
router.get('/stats', getAttendanceStats);

/**
 * @route   GET /api/attendance/monthly/:employeeId/:year/:month
 * @desc    Get comprehensive monthly attendance summary and report for an employee
 * @access  Private (requires authentication, employees can access own data, managers can access team data)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} employeeId - Employee ID (MongoDB ObjectId, required)
 * @param {number} year - Year (YYYY format, required, e.g., 2024)
 * @param {number} month - Month (1-12, required, e.g., 7 for July)
 *
 * @returns {Object} 200 - Monthly summary retrieved successfully
 * @returns {Object} returns.data.summary - Monthly attendance summary statistics
 * @returns {Array} returns.data.records - Detailed daily attendance records for the month
 * @returns {Object} returns.data.period - Period information (year, month, dates)
 * @returns {Object} 400 - Invalid parameters (employee ID, year, or month)
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions (cannot access other employee's data)
 * @returns {Object} 404 - Employee not found
 *
 * @example
 * GET /api/attendance/monthly/60d5ecb74b24a1234567890a/2024/7
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Monthly attendance retrieved successfully",
 *   "data": {
 *     "summary": {
 *       "totalWorkingDays": 22,
 *       "presentDays": 20,
 *       "absentDays": 2,
 *       "lateDays": 3,
 *       "totalHours": 176,
 *       "regularHours": 160,
 *       "overtimeHours": 16,
 *       "attendanceRate": 90.9
 *     },
 *     "records": [...],
 *     "period": {
 *       "year": 2024,
 *       "month": 7,
 *       "startDate": "2024-07-01T00:00:00.000Z",
 *       "endDate": "2024-07-31T23:59:59.999Z"
 *     }
 *   }
 * }
 */
router.get('/monthly/:employeeId/:year/:month', getMonthlyAttendance);

/**
 * @route   GET /api/attendance/:id
 * @desc    Get detailed information for a specific attendance record by ID
 * @access  Private (requires authentication, employees can access own records)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Attendance record ID (MongoDB ObjectId, required)
 *
 * @returns {Object} 200 - Attendance record retrieved successfully
 * @returns {Object} returns.data - Complete attendance record with calculated fields
 * @returns {Object} 400 - Invalid attendance record ID format
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 * @returns {Object} 404 - Attendance record not found
 *
 * @example
 * GET /api/attendance/60d5ecb74b24a1234567890d
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
router.get('/:id', getAttendanceById);

/**
 * ============================================================================
 * ATTENDANCE CRUD OPERATIONS
 * ============================================================================
 */

/**
 * @route   POST /api/attendance
 * @desc    Create a new attendance record (clock in) or manual attendance entry
 * @access  Private (requires authentication, employees can clock in for themselves)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @body {Object} attendanceData - Attendance record data
 * @body {string} [attendanceData.employee] - Employee ID (auto-filled for self, HR can specify)
 * @body {Date} [attendanceData.date] - Attendance date (defaults to current date)
 * @body {Date} [attendanceData.clockIn] - Clock in time (defaults to current time)
 * @body {string} [attendanceData.workLocation=office] - Work location (office, remote, client-site, travel)
 * @body {string} [attendanceData.status=present] - Attendance status (present, absent, late, half-day, overtime)
 * @body {string} [attendanceData.attendanceType=regular] - Type (regular, overtime, holiday, weekend, sick-leave, vacation)
 * @body {string} [attendanceData.notes] - Additional notes or comments (max 500 chars)
 * @body {Object} [attendanceData.location] - GPS location data (for mobile clock-in)
 * @body {number} [attendanceData.location.latitude] - GPS latitude
 * @body {number} [attendanceData.location.longitude] - GPS longitude
 * @body {string} [attendanceData.location.address] - Formatted address
 *
 * @returns {Object} 201 - Attendance record created successfully (clock in successful)
 * @returns {Object} returns.data - Created attendance record
 * @returns {Object} 400 - Validation error or invalid data
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 409 - Attendance record already exists for this date
 * @returns {Object} 422 - Business rule violation (e.g., already clocked in)
 *
 * @example
 * POST /api/attendance
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Content-Type: application/json
 *
 * {
 *   "workLocation": "office",
 *   "notes": "Starting work on project Alpha",
 *   "location": {
 *     "latitude": 40.7128,
 *     "longitude": -74.0060,
 *     "address": "123 Business St, New York, NY"
 *   }
 * }
 */
router.post('/', createAttendance);

/**
 * @route   PUT /api/attendance/:id
 * @desc    Update an attendance record (clock out, add breaks, modify details)
 * @access  Private (requires authentication, employees can update own records)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Attendance record ID (MongoDB ObjectId, required)
 *
 * @body {Object} updateData - Attendance update data (all fields optional)
 * @body {Date} [updateData.clockOut] - Clock out time
 * @body {Array<Object>} [updateData.breaks] - Array of break periods
 * @body {Date} [updateData.breaks[].startTime] - Break start time
 * @body {Date} [updateData.breaks[].endTime] - Break end time
 * @body {string} [updateData.breaks[].type] - Break type (lunch, coffee, personal, meeting)
 * @body {string} [updateData.breaks[].notes] - Break notes
 * @body {string} [updateData.status] - Updated attendance status
 * @body {string} [updateData.workLocation] - Updated work location
 * @body {string} [updateData.notes] - Updated notes
 * @body {Object} [updateData.clockOutLocation] - GPS location for clock out
 *
 * @returns {Object} 200 - Attendance record updated successfully
 * @returns {Object} returns.data - Updated attendance record with calculated totals
 * @returns {Object} 400 - Validation error or invalid data
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 * @returns {Object} 404 - Attendance record not found
 * @returns {Object} 422 - Business rule violation (e.g., clock out before clock in)
 *
 * @example
 * PUT /api/attendance/60d5ecb74b24a1234567890d
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Content-Type: application/json
 *
 * {
 *   "clockOut": "2024-07-02T17:30:00.000Z",
 *   "breaks": [
 *     {
 *       "startTime": "2024-07-02T12:00:00.000Z",
 *       "endTime": "2024-07-02T13:00:00.000Z",
 *       "type": "lunch",
 *       "notes": "Lunch break"
 *     }
 *   ],
 *   "notes": "Completed project Alpha milestone"
 * }
 */
router.put('/:id', updateAttendance);

/**
 * @route   DELETE /api/attendance/:id
 * @desc    Delete an attendance record (HR/Admin only, for corrections)
 * @access  Private (requires authentication, HR/Admin role required)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Attendance record ID (MongoDB ObjectId, required)
 *
 * @returns {Object} 200 - Attendance record deleted successfully
 * @returns {Object} returns.data - Confirmation of deletion
 * @returns {Object} 400 - Invalid attendance record ID format
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions (HR/Admin required)
 * @returns {Object} 404 - Attendance record not found
 * @returns {Object} 409 - Cannot delete attendance record (business rules)
 *
 * @note This operation should be used sparingly and only for data corrections.
 *       All deletions are logged for audit purposes.
 *
 * @example
 * DELETE /api/attendance/60d5ecb74b24a1234567890d
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Attendance record deleted successfully",
 *   "data": {
 *     "recordId": "60d5ecb74b24a1234567890d",
 *     "employee": "John Doe (EMP001)",
 *     "date": "2024-07-02",
 *     "deletedBy": "HR Admin",
 *     "deletionReason": "Data correction",
 *     "timestamp": "2024-07-02T15:30:00.000Z"
 *   }
 * }
 */
router.delete('/:id', deleteAttendance);

/**
 * ============================================================================
 * EXPORT ROUTER
 * ============================================================================
 */

export default router;
