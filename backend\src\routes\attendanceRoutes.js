/**
 * Attendance Routes
 * 
 * This module defines all HTTP routes for attendance operations.
 * Routes are organized by functionality and include proper middleware
 * for validation, authentication, and error handling.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import express from 'express';
import {
  getAllAttendance,
  getAttendanceById,
  createAttendance,
  updateAttendance,
  deleteAttendance,
  getAttendanceStats,
  getMonthlyAttendance
} from '../controllers/attendanceController.js';

const router = express.Router();

/**
 * @route   GET /api/attendance
 * @desc    Get all attendance records with optional filtering and pagination
 * @access  Public (should be protected in production)
 * @params  Query parameters:
 *          - page: Page number (default: 1)
 *          - limit: Items per page (default: 10)
 *          - employee: Filter by employee ID
 *          - status: Filter by status (present/absent/late/half-day/overtime)
 *          - attendanceType: Filter by type (regular/overtime/holiday/weekend/sick-leave/vacation)
 *          - dateFrom: Start date for date range filter
 *          - dateTo: End date for date range filter
 *          - sortBy: Field to sort by (default: date)
 *          - sortOrder: Sort direction (asc/desc, default: desc)
 */
router.get('/', getAllAttendance);

/**
 * @route   GET /api/attendance/stats
 * @desc    Get attendance statistics and analytics
 * @access  Public (should be protected in production)
 */
router.get('/stats', getAttendanceStats);

/**
 * @route   GET /api/attendance/monthly/:employeeId/:year/:month
 * @desc    Get monthly attendance summary for an employee
 * @access  Public (should be protected in production)
 * @params  Route parameters:
 *          - employeeId: Employee ID (MongoDB ObjectId)
 *          - year: Year (YYYY)
 *          - month: Month (1-12)
 */
router.get('/monthly/:employeeId/:year/:month', getMonthlyAttendance);

/**
 * @route   GET /api/attendance/:id
 * @desc    Get a single attendance record by ID
 * @access  Public (should be protected in production)
 * @params  Route parameters:
 *          - id: Attendance record ID (MongoDB ObjectId)
 */
router.get('/:id', getAttendanceById);

/**
 * @route   POST /api/attendance
 * @desc    Create a new attendance record (clock in)
 * @access  Public (should be protected in production)
 * @body    Attendance data:
 *          - employee: Employee ID (required)
 *          - date: Date (default: current date)
 *          - clockIn: Clock in time (default: current time)
 *          - workLocation: Work location (office/remote/client-site/travel)
 *          - status: Status (present/absent/late/half-day/overtime)
 *          - attendanceType: Type (regular/overtime/holiday/weekend/sick-leave/vacation)
 *          - notes: Additional notes
 */
router.post('/', createAttendance);

/**
 * @route   PUT /api/attendance/:id
 * @desc    Update an attendance record (clock out, add breaks, etc.)
 * @access  Public (should be protected in production)
 * @params  Route parameters:
 *          - id: Attendance record ID (MongoDB ObjectId)
 * @body    Updated attendance data:
 *          - clockOut: Clock out time
 *          - breaks: Array of break objects
 *          - notes: Additional notes
 *          - status: Updated status
 */
router.put('/:id', updateAttendance);

/**
 * @route   DELETE /api/attendance/:id
 * @desc    Delete an attendance record
 * @access  Public (should be protected in production)
 * @params  Route parameters:
 *          - id: Attendance record ID (MongoDB ObjectId)
 */
router.delete('/:id', deleteAttendance);

export default router;
