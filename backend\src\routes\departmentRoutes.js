/**
 * Department Routes
 * 
 * This module defines all HTTP routes for department operations.
 * Routes are organized by functionality and include proper middleware
 * for validation, authentication, and error handling.
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import express from 'express';
import {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentStats
} from '../controllers/departmentController.js';

const router = express.Router();

/**
 * @route   GET /api/departments
 * @desc    Get all departments with optional filtering and pagination
 * @access  Public (should be protected in production)
 * @params  Query parameters:
 *          - page: Page number (default: 1)
 *          - limit: Items per page (default: 10)
 *          - status: Filter by status (active/inactive)
 *          - search: Search in name, code, description
 *          - sortBy: Field to sort by (default: name)
 *          - sortOrder: Sort direction (asc/desc, default: asc)
 */
router.get('/', getAllDepartments);

/**
 * @route   GET /api/departments/stats
 * @desc    Get department statistics and analytics
 * @access  Public (should be protected in production)
 */
router.get('/stats', getDepartmentStats);

/**
 * @route   GET /api/departments/:id
 * @desc    Get a single department by ID
 * @access  Public (should be protected in production)
 * @params  Route parameters:
 *          - id: Department ID (MongoDB ObjectId)
 */
router.get('/:id', getDepartmentById);

/**
 * @route   POST /api/departments
 * @desc    Create a new department
 * @access  Public (should be protected in production)
 * @body    Department data:
 *          - name: Department name (required)
 *          - code: Department code (required, unique)
 *          - description: Department description
 *          - manager: Manager employee ID
 *          - location: Location object
 *          - contactInfo: Contact information
 *          - budget: Budget information
 *          - parentDepartment: Parent department ID
 */
router.post('/', createDepartment);

/**
 * @route   PUT /api/departments/:id
 * @desc    Update an existing department
 * @access  Public (should be protected in production)
 * @params  Route parameters:
 *          - id: Department ID (MongoDB ObjectId)
 * @body    Updated department data (partial updates allowed)
 */
router.put('/:id', updateDepartment);

/**
 * @route   DELETE /api/departments/:id
 * @desc    Delete a department (soft delete - sets status to inactive)
 * @access  Public (should be protected in production)
 * @params  Route parameters:
 *          - id: Department ID (MongoDB ObjectId)
 * @note    Cannot delete departments with active employees
 */
router.delete('/:id', deleteDepartment);

export default router;
