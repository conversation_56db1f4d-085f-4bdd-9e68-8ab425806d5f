/**
 * ============================================================================
 * DEPARTMENT MANAGEMENT ROUTES
 * ============================================================================
 *
 * This module defines all HTTP routes for department operations including
 * CRUD operations, organizational hierarchy management, employee assignment,
 * and departmental statistics and analytics.
 *
 * @fileoverview Department management and organizational structure routes
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 1.0.0
 * @created 2024-01-01
 * @updated 2024-07-02
 *
 * @description
 * Department routes handle:
 * - Department CRUD operations (Create, Read, Update, Delete)
 * - Organizational hierarchy management
 * - Employee assignment and management
 * - Department statistics and analytics
 * - Budget tracking and management
 * - Manager assignment and delegation
 *
 * Security:
 * - All routes require authentication
 * - Role-based access control (HR/Admin for modifications)
 * - Input validation and sanitization
 * - Soft delete for data integrity
 */

import express from 'express';
import {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentStats
} from '../controllers/departmentController.js';

const router = express.Router();

/**
 * ============================================================================
 * DEPARTMENT LISTING & ANALYTICS ROUTES
 * ============================================================================
 */

/**
 * @route   GET /api/departments
 * @desc    Get paginated list of departments with hierarchy and filtering options
 * @access  Private (requires authentication)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @query {number} [page=1] - Page number for pagination (min: 1)
 * @query {number} [limit=10] - Number of items per page (min: 1, max: 100)
 * @query {string} [search] - Search term for name, code, or description
 * @query {string} [status] - Filter by status (active, inactive)
 * @query {string} [parentDepartment] - Filter by parent department ID
 * @query {boolean} [includeEmployeeCount=true] - Include employee count in response
 * @query {boolean} [includeHierarchy=false] - Include full hierarchy structure
 * @query {string} [sortBy=name] - Sort field (name, code, createdAt, employeeCount)
 * @query {string} [sortOrder=asc] - Sort direction (asc, desc)
 *
 * @returns {Object} 200 - Departments retrieved successfully
 * @returns {Array} returns.data.departments - Array of department objects
 * @returns {Object} returns.data.pagination - Pagination metadata
 * @returns {Object} returns.data.hierarchy - Organizational hierarchy (if requested)
 * @returns {Object} 400 - Invalid query parameters
 * @returns {Object} 401 - Authentication required
 *
 * @example
 * GET /api/departments?page=1&limit=20&includeEmployeeCount=true&sortBy=name
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Departments retrieved successfully",
 *   "data": {
 *     "departments": [
 *       {
 *         "_id": "60d5ecb74b24a1234567890b",
 *         "name": "Engineering",
 *         "code": "ENG",
 *         "description": "Software development and engineering",
 *         "status": "active",
 *         "manager": {
 *           "name": "John Smith",
 *           "employeeId": "EMP002"
 *         },
 *         "employeeCount": 25,
 *         "budget": {
 *           "annual": 2500000,
 *           "currency": "USD"
 *         },
 *         "location": {
 *           "building": "Tech Tower",
 *           "floor": 3
 *         }
 *       }
 *     ],
 *     "pagination": {
 *       "currentPage": 1,
 *       "totalPages": 3,
 *       "totalItems": 12,
 *       "itemsPerPage": 20
 *     }
 *   }
 * }
 */
router.get('/', getAllDepartments);

/**
 * @route   GET /api/departments/stats
 * @desc    Get comprehensive department statistics and organizational analytics
 * @access  Private (requires authentication, HR/Admin preferred)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @returns {Object} 200 - Statistics retrieved successfully
 * @returns {Object} returns.data.overview - General department statistics
 * @returns {number} returns.data.overview.totalDepartments - Total number of departments
 * @returns {number} returns.data.overview.activeDepartments - Number of active departments
 * @returns {number} returns.data.overview.totalEmployees - Total employees across all departments
 * @returns {Array} returns.data.departmentSizes - Departments by employee count
 * @returns {Array} returns.data.budgetDistribution - Budget allocation by department
 * @returns {Object} returns.data.hierarchyDepth - Organizational hierarchy statistics
 * @returns {Array} returns.data.managerDistribution - Manager assignment statistics
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 *
 * @example
 * GET /api/departments/stats
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
router.get('/stats', getDepartmentStats);

/**
 * @route   GET /api/departments/:id
 * @desc    Get detailed information for a specific department by ID
 * @access  Private (requires authentication)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Department ID (MongoDB ObjectId, required)
 *
 * @query {boolean} [includeEmployees=false] - Include list of department employees
 * @query {boolean} [includeSubDepartments=false] - Include sub-departments
 * @query {boolean} [includeStats=false] - Include department statistics
 *
 * @returns {Object} 200 - Department retrieved successfully
 * @returns {Object} returns.data - Complete department information
 * @returns {Object} 400 - Invalid department ID format
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 404 - Department not found
 *
 * @example
 * GET /api/departments/60d5ecb74b24a1234567890b?includeEmployees=true&includeStats=true
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
router.get('/:id', getDepartmentById);

/**
 * ============================================================================
 * DEPARTMENT CRUD OPERATIONS
 * ============================================================================
 */

/**
 * @route   POST /api/departments
 * @desc    Create a new department in the organizational structure
 * @access  Private (requires authentication, HR/Admin role required)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @body {Object} departmentData - New department information
 * @body {string} departmentData.name - Department name (required, 2-100 chars, unique)
 * @body {string} departmentData.code - Department code (required, 2-10 chars, unique, uppercase)
 * @body {string} [departmentData.description] - Department description (max 500 chars)
 * @body {string} [departmentData.manager] - Manager employee ID (must be valid employee)
 * @body {string} [departmentData.parentDepartment] - Parent department ID (for hierarchy)
 * @body {string} [departmentData.status=active] - Department status (active, inactive)
 * @body {Object} [departmentData.location] - Physical location information
 * @body {string} [departmentData.location.building] - Building name
 * @body {number} [departmentData.location.floor] - Floor number
 * @body {string} [departmentData.location.room] - Room number/identifier
 * @body {Object} [departmentData.contactInfo] - Contact information
 * @body {string} [departmentData.contactInfo.email] - Department email
 * @body {string} [departmentData.contactInfo.phone] - Department phone
 * @body {string} [departmentData.contactInfo.extension] - Phone extension
 * @body {Object} [departmentData.budget] - Budget information
 * @body {number} [departmentData.budget.annual] - Annual budget amount
 * @body {string} [departmentData.budget.currency=USD] - Budget currency
 * @body {number} [departmentData.budget.fiscalYear] - Fiscal year
 *
 * @returns {Object} 201 - Department created successfully
 * @returns {Object} returns.data - Created department information
 * @returns {Object} 400 - Validation error or invalid data
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions (HR/Admin required)
 * @returns {Object} 409 - Department name or code already exists
 *
 * @example
 * POST /api/departments
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Content-Type: application/json
 *
 * {
 *   "name": "Data Science",
 *   "code": "DS",
 *   "description": "Data analysis and machine learning team",
 *   "manager": "60d5ecb74b24a1234567890c",
 *   "parentDepartment": "60d5ecb74b24a1234567890b",
 *   "location": {
 *     "building": "Tech Tower",
 *     "floor": 4,
 *     "room": "401-450"
 *   },
 *   "budget": {
 *     "annual": 1500000,
 *     "currency": "USD",
 *     "fiscalYear": 2024
 *   }
 * }
 */
router.post('/', createDepartment);

/**
 * @route   PUT /api/departments/:id
 * @desc    Update an existing department's information
 * @access  Private (requires authentication, HR/Admin role required)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Department ID (MongoDB ObjectId, required)
 *
 * @body {Object} updateData - Department update data (all fields optional)
 * @body {string} [updateData.name] - Department name (2-100 chars, unique)
 * @body {string} [updateData.code] - Department code (2-10 chars, unique, uppercase)
 * @body {string} [updateData.description] - Department description (max 500 chars)
 * @body {string} [updateData.manager] - Manager employee ID (must be valid employee)
 * @body {string} [updateData.parentDepartment] - Parent department ID
 * @body {string} [updateData.status] - Department status (active, inactive)
 * @body {Object} [updateData.location] - Physical location information
 * @body {Object} [updateData.contactInfo] - Contact information
 * @body {Object} [updateData.budget] - Budget information
 *
 * @returns {Object} 200 - Department updated successfully
 * @returns {Object} returns.data - Updated department information
 * @returns {Object} 400 - Validation error or invalid data
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions (HR/Admin required)
 * @returns {Object} 404 - Department not found
 * @returns {Object} 409 - Department name or code already exists
 * @returns {Object} 422 - Cannot set parent department (would create circular reference)
 *
 * @example
 * PUT /api/departments/60d5ecb74b24a1234567890b
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Content-Type: application/json
 *
 * {
 *   "description": "Advanced software development and engineering solutions",
 *   "budget": {
 *     "annual": 2750000,
 *     "fiscalYear": 2024
 *   }
 * }
 */
router.put('/:id', updateDepartment);

/**
 * @route   DELETE /api/departments/:id
 * @desc    Delete a department (soft delete - sets status to inactive)
 * @access  Private (requires authentication, HR/Admin role required)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Department ID (MongoDB ObjectId, required)
 *
 * @returns {Object} 200 - Department deleted successfully
 * @returns {Object} returns.data - Confirmation of deletion with final department state
 * @returns {Object} 400 - Invalid department ID format
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions (HR/Admin required)
 * @returns {Object} 404 - Department not found
 * @returns {Object} 409 - Cannot delete department with active employees or sub-departments
 *
 * @note This performs a soft delete by setting the department status to 'inactive'
 *       and preserving all historical data for compliance and reporting purposes.
 *       Departments with active employees or active sub-departments cannot be deleted.
 *
 * @example
 * DELETE /api/departments/60d5ecb74b24a1234567890b
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Department deleted successfully",
 *   "data": {
 *     "departmentId": "60d5ecb74b24a1234567890b",
 *     "name": "Engineering",
 *     "code": "ENG",
 *     "status": "inactive",
 *     "deactivationDate": "2024-07-02T10:30:00.000Z",
 *     "employeeCount": 0,
 *     "subDepartmentCount": 0
 *   }
 * }
 */
router.delete('/:id', deleteDepartment);

/**
 * ============================================================================
 * EXPORT ROUTER
 * ============================================================================
 */

export default router;
