[{"id": "dept-001", "name": "Engineering", "description": "Software development and technical operations", "manager": "<PERSON>", "employeeCount": 15, "budget": 2500000, "createdAt": "2022-01-01T00:00:00.000Z", "updatedAt": "2022-01-01T00:00:00.000Z"}, {"id": "dept-002", "name": "Human Resources", "description": "Employee relations and organizational development", "manager": "<PERSON>", "employeeCount": 5, "budget": 800000, "createdAt": "2022-01-01T00:00:00.000Z", "updatedAt": "2022-01-01T00:00:00.000Z"}, {"id": "dept-003", "name": "Marketing", "description": "Brand promotion and customer acquisition", "manager": "<PERSON>", "employeeCount": 8, "budget": 1200000, "createdAt": "2022-01-01T00:00:00.000Z", "updatedAt": "2022-01-01T00:00:00.000Z"}, {"id": "dept-004", "name": "Product", "description": "Product strategy and management", "manager": "<PERSON>", "employeeCount": 12, "budget": 1800000, "createdAt": "2022-01-01T00:00:00.000Z", "updatedAt": "2022-01-01T00:00:00.000Z"}, {"id": "dept-005", "name": "Finance", "description": "Financial planning and accounting", "manager": "<PERSON>", "employeeCount": 6, "budget": 900000, "createdAt": "2022-01-01T00:00:00.000Z", "updatedAt": "2022-01-01T00:00:00.000Z"}, {"id": "dept-006", "name": "Design", "description": "User experience and visual design", "manager": "<PERSON>", "employeeCount": 4, "budget": 600000, "createdAt": "2022-01-01T00:00:00.000Z", "updatedAt": "2022-01-01T00:00:00.000Z"}]