.page-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.filters-section {
  background: var(--color-bg-primary);
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border-medium);
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-input::placeholder {
  color: var(--color-text-tertiary);
}

.filter-controls {
  display: flex;
  gap: 1rem;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border-medium);
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.employees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.employee-card {
  background: var(--color-bg-primary);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  transition: all 0.2s ease;
  position: relative;
}

.employee-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-border-medium);
}

.employee-avatar {
  text-align: center;
  margin-bottom: 1rem;
}

.avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 auto;
  text-transform: uppercase;
}

.employee-info {
  text-align: center;
}

.employee-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--color-text-primary);
}

.employee-position {
  font-size: 0.875rem;
  color: var(--color-primary);
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.employee-department {
  color: var(--color-text-secondary);
  font-size: 0.8125rem;
  margin-bottom: 1rem;
}

.employee-contact {
  margin-bottom: 1rem;
}

.employee-email,
.employee-phone {
  font-size: 0.8125rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.25rem;
}

.employee-status {
  margin-bottom: 1rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.active {
  background-color: #f0fdf4;
  color: var(--color-success);
  border: 1px solid #bbf7d0;
}

.status-badge.on-leave {
  background-color: #fffbeb;
  color: var(--color-warning);
  border: 1px solid #fed7aa;
}

.status-badge.inactive {
  background-color: #fef2f2;
  color: var(--color-error);
  border: 1px solid #fecaca;
}

.employee-actions {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.btn-icon {
  background: none;
  border: 1px solid var(--color-border-light);
  font-size: 1rem;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-text-secondary);
}

.btn-icon:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-border-medium);
  color: var(--color-text-primary);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 2rem 0;
}

.pagination-info {
  color: var(--color-text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filter-controls {
    justify-content: center;
  }
  
  .employees-grid {
    grid-template-columns: 1fr;
  }
}
