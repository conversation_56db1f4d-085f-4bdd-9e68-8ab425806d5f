.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  padding: 40px;
  max-width: 450px;
  width: 100%;
  animation: slideIn 0.6s ease-out;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 35px;
}

.login-header h1 {
  color: #2d3748;
  font-size: 2.5rem;
  margin-bottom: 8px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  color: #718096;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

.login-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #2d3748;
  font-weight: 600;
  font-size: 0.95rem;
}

.form-group input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: #f8fafc;
  font-family: inherit;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: white;
  transform: translateY(-1px);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.form-group input::placeholder {
  color: #a0aec0;
}

.error-message {
  background: linear-gradient(135deg, #feb2b2 0%, #fed7d7 100%);
  color: #c53030;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  border: 1px solid #fed7d7;
  display: flex;
  align-items: center;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.login-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-family: inherit;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.login-button:hover:not(:disabled)::before {
  left: 100%;
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.demo-section {
  border-top: 1px solid #e2e8f0;
  padding-top: 25px;
  margin-bottom: 25px;
}

.demo-toggle {
  width: 100%;
  background: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: inherit;
}

.demo-toggle:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.demo-accounts {
  margin-top: 16px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.demo-accounts h3 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 1.1rem;
  text-align: center;
  font-weight: 600;
}

.demo-password {
  text-align: center;
  color: #718096;
  font-size: 0.85rem;
  margin: 0 0 16px 0;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.demo-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  text-align: left;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  color: white;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  font-family: inherit;
}

.demo-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.3s;
}

.demo-btn:hover::before {
  left: 100%;
}

.demo-btn strong {
  font-size: 0.95rem;
}

.demo-btn small {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: 2px;
  font-weight: normal;
}

.demo-btn.admin {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

.demo-btn.admin:hover {
  background: linear-gradient(135deg, #c53030 0%, #9c2626 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(197, 48, 48, 0.3);
}

.demo-btn.manager {
  background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
}

.demo-btn.manager:hover {
  background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
}

.demo-btn.hr {
  background: linear-gradient(135deg, #805ad5 0%, #6b46c1 100%);
}

.demo-btn.hr:hover {
  background: linear-gradient(135deg, #6b46c1 0%, #553c9a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(128, 90, 213, 0.3);
}

.demo-btn.employee {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.demo-btn.employee:hover {
  background: linear-gradient(135deg, #2f855a 0%, #276749 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
}

.demo-btn.marketing {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.demo-btn.marketing:hover {
  background: linear-gradient(135deg, #dd6b20 0%, #c05621 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3);
}

.login-footer {
  text-align: center;
  border-top: 1px solid #e2e8f0;
  padding-top: 20px;
}

.login-footer p {
  color: #718096;
  font-size: 0.85rem;
  margin: 0;
}

/* Responsive design */
@media (max-width: 480px) {
  .login-container {
    padding: 15px;
  }
  
  .login-card {
    padding: 30px 20px;
  }
  
  .login-header h1 {
    font-size: 2rem;
  }
  
  .form-group input {
    padding: 12px 14px;
  }
  
  .login-button {
    padding: 14px;
  }
  
  .demo-buttons {
    gap: 8px;
  }
  
  .demo-btn {
    padding: 10px 12px;
  }
  
  .demo-btn strong {
    font-size: 0.9rem;
  }
  
  .demo-btn small {
    font-size: 0.75rem;
  }
}

@media (max-width: 360px) {
  .login-header h1 {
    font-size: 1.8rem;
  }
  
  .demo-accounts {
    padding: 16px;
  }
}
