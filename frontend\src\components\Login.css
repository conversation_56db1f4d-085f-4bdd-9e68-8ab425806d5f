.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-secondary);
  padding: 2rem;
  position: relative;
}

.login-card {
  background: var(--color-bg-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  padding: 2.5rem;
  max-width: 400px;
  width: 100%;
  animation: slideIn 0.3s ease-out;
  border: 1px solid var(--color-border-light);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  color: var(--color-text-primary);
  font-size: 1.875rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.login-header p {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 400;
}

.login-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-primary);
  font-weight: 500;
  font-size: 0.875rem;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border-medium);
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
  background: var(--color-bg-primary);
  font-family: inherit;
  color: var(--color-text-primary);
}

.form-group input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.form-group input::placeholder {
  color: var(--color-text-tertiary);
}

.error-message {
  background: #fef2f2;
  color: var(--color-error);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin-bottom: 1.25rem;
  font-size: 0.875rem;
  border: 1px solid #fecaca;
  display: flex;
  align-items: center;
}

.login-button {
  width: 100%;
  background: var(--color-primary);
  color: white;
  border: 1px solid var(--color-primary);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.login-button:hover:not(:disabled) {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.login-button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.demo-section {
  border-top: 1px solid var(--color-border-light);
  padding-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.demo-toggle {
  width: 100%;
  background: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border-medium);
  padding: 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: inherit;
}

.demo-toggle:hover {
  background: var(--color-gray-100);
  border-color: var(--color-border-dark);
  color: var(--color-text-primary);
}

.demo-accounts {
  margin-top: 1rem;
  padding: 1.25rem;
  background: var(--color-bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--color-border-light);
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.demo-accounts h3 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text-primary);
  font-size: 1rem;
  text-align: center;
  font-weight: 600;
}

.demo-password {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 0.8125rem;
  margin: 0 0 1rem 0;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.demo-btn {
  padding: 0.75rem 1rem;
  border: 1px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  text-align: left;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  color: white;
  font-weight: 500;
  font-family: inherit;
}

.demo-btn strong {
  font-size: 0.875rem;
}

.demo-btn small {
  font-size: 0.75rem;
  opacity: 0.9;
  margin-top: 0.125rem;
  font-weight: normal;
}

.demo-btn.admin {
  background: var(--color-error);
  border-color: var(--color-error);
}

.demo-btn.admin:hover {
  background: #b91c1c;
  border-color: #b91c1c;
}

.demo-btn.manager {
  background: var(--color-info);
  border-color: var(--color-info);
}

.demo-btn.manager:hover {
  background: #0369a1;
  border-color: #0369a1;
}

.demo-btn.hr {
  background: #7c3aed;
  border-color: #7c3aed;
}

.demo-btn.hr:hover {
  background: #6d28d9;
  border-color: #6d28d9;
}

.demo-btn.employee {
  background: var(--color-success);
  border-color: var(--color-success);
}

.demo-btn.employee:hover {
  background: #047857;
  border-color: #047857;
}

.demo-btn.marketing {
  background: var(--color-warning);
  border-color: var(--color-warning);
}

.demo-btn.marketing:hover {
  background: #c2410c;
  border-color: #c2410c;
}

.login-footer {
  text-align: center;
  border-top: 1px solid var(--color-border-light);
  padding-top: 1.25rem;
}

.login-footer p {
  color: var(--color-text-secondary);
  font-size: 0.8125rem;
  margin: 0;
}

/* Responsive design */
@media (max-width: 480px) {
  .login-container {
    padding: 1rem;
  }

  .login-card {
    padding: 2rem 1.5rem;
  }

  .login-header h1 {
    font-size: 1.5rem;
  }

  .form-group input {
    padding: 0.75rem;
  }

  .login-button {
    padding: 0.75rem;
  }

  .demo-buttons {
    gap: 0.375rem;
  }

  .demo-btn {
    padding: 0.625rem 0.75rem;
  }

  .demo-btn strong {
    font-size: 0.8125rem;
  }

  .demo-btn small {
    font-size: 0.6875rem;
  }
}

@media (max-width: 360px) {
  .login-header h1 {
    font-size: 1.375rem;
  }

  .demo-accounts {
    padding: 1rem;
  }
}
