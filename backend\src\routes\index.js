/**
 * ============================================================================
 * HRMS API ROUTES CONFIGURATION
 * ============================================================================
 *
 * Central hub for all API routes in the Human Resource Management System.
 * This module organizes and registers all route modules with proper prefixes,
 * middleware, and comprehensive documentation.
 *
 * @fileoverview Main API routes configuration and documentation
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 1.0.0
 * @created 2024-01-01
 * @updated 2024-07-02
 *
 * @description
 * This file serves as the central routing hub for the HRMS API. It:
 * - Registers all route modules with appropriate prefixes
 * - Provides comprehensive API documentation
 * - Implements health check and info endpoints
 * - Handles 404 errors for undefined routes
 *
 * @example
 * // Import and use in Express app
 * import apiRoutes from './routes/index.js';
 * app.use('/api', apiRoutes);
 */

import express from "express";
import authRoutes from './authRoutes.js';
import employeeRoutes from './employeeRoutes.js';
import departmentRoutes from './departmentRoutes.js';
import attendanceRoutes from './attendanceRoutes.js';

const router = express.Router();

/**
 * ============================================================================
 * API ROUTE REGISTRATION
 * ============================================================================
 *
 * All routes are prefixed with /api in the main app.js file
 *
 * Route Structure:
 * ┌─────────────────────┬──────────────────────────────────────────────────┐
 * │ Module              │ Base Path & Description                          │
 * ├─────────────────────┼──────────────────────────────────────────────────┤
 * │ Authentication      │ /api/auth/* - Login, registration, profile mgmt  │
 * │ Employee Management │ /api/employees/* - CRUD, search, statistics      │
 * │ Department Mgmt     │ /api/departments/* - CRUD, hierarchy, assignment │
 * │ Attendance Tracking │ /api/attendance/* - Clock in/out, reports, stats │
 * │ System Utilities    │ /api/health, /api/info - Health & documentation  │
 * └─────────────────────┴──────────────────────────────────────────────────┘
 */

/**
 * Authentication Module
 * Handles user authentication, registration, and profile management
 * Base path: /api/auth
 */
router.use('/auth', authRoutes);

/**
 * Employee Management Module
 * Handles employee CRUD operations, search, and statistics
 * Base path: /api/employees
 */
router.use('/employees', employeeRoutes);

/**
 * Department Management Module
 * Handles department CRUD operations and organizational hierarchy
 * Base path: /api/departments
 */
router.use('/departments', departmentRoutes);

/**
 * Attendance Management Module
 * Handles time tracking, clock in/out, and attendance reporting
 * Base path: /api/attendance
 */
router.use('/attendance', attendanceRoutes);

/**
 * ============================================================================
 * SYSTEM UTILITY ENDPOINTS
 * ============================================================================
 */

/**
 * @route   GET /api/health
 * @desc    Health check endpoint to verify API status and connectivity
 * @access  Public
 * @returns {Object} API health status information
 *
 * @example
 * GET /api/health
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "HRMS API is running successfully",
 *   "status": "healthy",
 *   "timestamp": "2024-07-02T10:30:00.000Z",
 *   "version": "2.0.0",
 *   "uptime": 3600.123,
 *   "environment": "development"
 * }
 */
router.get('/health', (_, res) => {
  res.status(200).json({
    success: true,
    message: 'HRMS API is running successfully',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    database: 'connected' // TODO: Add actual DB health check
  });
});

/**
 * @route   GET /api/info
 * @desc    Comprehensive API documentation and endpoint information
 * @access  Public
 * @returns {Object} Complete API documentation with all available endpoints
 *
 * @example
 * GET /api/info
 *
 * Response includes:
 * - API version and metadata
 * - All available endpoints with methods and descriptions
 * - Authentication requirements
 * - Response format specifications
 * - Usage examples and guidelines
 */
router.get('/info', (_, res) => {
  res.status(200).json({
    success: true,
    message: 'HRMS API Documentation',
    data: {
      api: {
        name: 'Human Resource Management System API',
        version: '2.0.0',
        description: 'Comprehensive HRMS API for employee, department, and attendance management',
        baseUrl: `${req.protocol}://${req.get('host')}/api`,
        documentation: 'https://docs.hrms.com/api',
        support: '<EMAIL>'
      },
      authentication: {
        type: 'JWT Bearer Token',
        header: 'Authorization: Bearer <token>',
        endpoints: {
          login: 'POST /api/auth/login',
          register: 'POST /api/auth/register',
          refresh: 'POST /api/auth/refresh'
        }
      },
      responseFormat: {
        success: {
          success: true,
          message: 'string',
          data: 'object|array',
          timestamp: 'ISO 8601 string'
        },
        error: {
          success: false,
          error: 'string',
          message: 'string',
          code: 'string (optional)',
          timestamp: 'ISO 8601 string'
        }
      },
      modules: {
        authentication: {
          base: '/api/auth',
          description: 'User authentication and profile management',
          endpoints: {
            'POST /login': 'Authenticate user and return JWT token',
            'POST /register': 'Register new user account',
            'GET /profile': 'Get current user profile (requires auth)',
            'PUT /profile': 'Update user profile (requires auth)',
            'POST /change-password': 'Change user password (requires auth)',
            'POST /logout': 'Logout user (requires auth)',
            'POST /refresh': 'Refresh JWT token'
          },
          authentication: 'Required for all except login, register, refresh'
        },
        employees: {
          base: '/api/employees',
          description: 'Employee management and operations',
          endpoints: {
            'GET /': 'List employees with pagination and filtering',
            'GET /stats': 'Get employee statistics and analytics',
            'GET /search': 'Advanced employee search with filters',
            'GET /:id': 'Get specific employee by ID',
            'POST /': 'Create new employee',
            'PUT /:id': 'Update existing employee',
            'DELETE /:id': 'Delete employee (soft delete)'
          },
          queryParameters: {
            page: 'Page number (default: 1)',
            limit: 'Items per page (default: 10)',
            search: 'Search term for name, email, or employee ID',
            department: 'Filter by department ID',
            status: 'Filter by status (active, inactive, terminated)',
            sortBy: 'Sort field (name, email, createdAt, etc.)',
            sortOrder: 'Sort direction (asc, desc)'
          },
          authentication: 'Required for all operations'
        },
        departments: {
          base: '/api/departments',
          description: 'Department management and organizational structure',
          endpoints: {
            'GET /': 'List departments with hierarchy information',
            'GET /stats': 'Get department statistics and metrics',
            'GET /:id': 'Get specific department by ID',
            'POST /': 'Create new department',
            'PUT /:id': 'Update existing department',
            'DELETE /:id': 'Delete department (if no active employees)'
          },
          features: [
            'Hierarchical department structure',
            'Employee count tracking',
            'Budget management',
            'Manager assignment'
          ],
          authentication: 'Required for all operations'
        },
        attendance: {
          base: '/api/attendance',
          description: 'Time tracking and attendance management',
          endpoints: {
            'GET /': 'List attendance records with filtering',
            'GET /stats': 'Get attendance statistics and analytics',
            'GET /monthly/:employeeId/:year/:month': 'Get monthly attendance summary',
            'GET /:id': 'Get specific attendance record',
            'POST /': 'Clock in or create attendance record',
            'PUT /:id': 'Clock out or update attendance record',
            'DELETE /:id': 'Delete attendance record'
          },
          features: [
            'Clock in/out functionality',
            'Break time tracking',
            'Overtime calculation',
            'Monthly and yearly reports',
            'Attendance statistics'
          ],
          authentication: 'Required for all operations'
        }
      },
      utilities: {
        'GET /api/health': 'API health check and status',
        'GET /api/info': 'This comprehensive API documentation'
      }
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * ============================================================================
 * ERROR HANDLING & 404 ROUTES
 * ============================================================================
 */

/**
 * @route   * /api/*
 * @desc    Handle undefined API endpoints with helpful error messages
 * @access  Public
 * @returns {Object} 404 error with available endpoints information
 *
 * @example
 * GET /api/nonexistent
 *
 * Response:
 * {
 *   "success": false,
 *   "error": "API endpoint not found",
 *   "message": "The endpoint GET /api/nonexistent does not exist",
 *   "suggestions": [...],
 *   "documentation": "/api/info",
 *   "timestamp": "2024-07-02T10:30:00.000Z"
 * }
 */
router.use('*', (req, res) => {
  const requestedPath = req.originalUrl;
  const method = req.method;

  // Suggest similar endpoints based on the requested path
  const suggestions = [];
  if (requestedPath.includes('employee')) {
    suggestions.push('/api/employees', '/api/employees/stats', '/api/employees/search');
  }
  if (requestedPath.includes('department')) {
    suggestions.push('/api/departments', '/api/departments/stats');
  }
  if (requestedPath.includes('attendance')) {
    suggestions.push('/api/attendance', '/api/attendance/stats');
  }
  if (requestedPath.includes('auth') || requestedPath.includes('login')) {
    suggestions.push('/api/auth/login', '/api/auth/register', '/api/auth/profile');
  }

  res.status(404).json({
    success: false,
    error: 'API endpoint not found',
    message: `The endpoint ${method} ${requestedPath} does not exist`,
    suggestions: suggestions.length > 0 ? suggestions : [
      '/api/info - Complete API documentation',
      '/api/health - API health check',
      '/api/auth/* - Authentication endpoints',
      '/api/employees/* - Employee management',
      '/api/departments/* - Department management',
      '/api/attendance/* - Attendance tracking'
    ],
    documentation: '/api/info',
    timestamp: new Date().toISOString()
  });
});

/**
 * ============================================================================
 * EXPORT ROUTER
 * ============================================================================
 */

export default router;