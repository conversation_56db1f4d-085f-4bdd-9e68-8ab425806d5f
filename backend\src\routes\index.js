/**
 * Main Routes Index
 * 
 * This module serves as the central hub for all API routes.
 * It registers and organizes all route modules under their
 * respective base paths.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import express from "express";
import authRoutes from './authRoutes.js';
import employeeRoutes from './employeeRoutes.js';
import departmentRoutes from './departmentRoutes.js';
import attendanceRoutes from './attendanceRoutes.js';

const router = express.Router();

/**
 * API Route Registration
 *
 * All routes are prefixed with /api in the main app.js file
 * Routes registered here will be available at:
 * - /api/auth/* - Authentication endpoints
 * - /api/employees/* - Employee management endpoints
 * - /api/departments/* - Department management endpoints
 * - /api/attendance/* - Attendance management endpoints
 */

// Authentication routes - handles login, registration, profile management
router.use('/auth', authRoutes);

// Employee routes - handles all employee CRUD operations
router.use('/employees', employeeRoutes);

// Department routes - handles all department CRUD operations
router.use('/departments', departmentRoutes);

// Attendance routes - handles all attendance CRUD operations
router.use('/attendance', attendanceRoutes);

/**
 * API Health Check Endpoint
 * Simple endpoint to verify API is running
 */
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'HRMS API is running successfully',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

/**
 * API Info Endpoint
 * Provides information about available endpoints
 */
router.get('/info', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'HRMS API Information',
    data: {
      version: '1.0.0',
      endpoints: {
        auth: {
          base: '/api/auth',
          methods: ['GET', 'POST', 'PUT'],
          features: [
            'Login/Logout',
            'Registration',
            'Profile management',
            'Password change',
            'Token refresh'
          ]
        },
        employees: {
          base: '/api/employees',
          methods: ['GET', 'POST', 'PUT', 'DELETE'],
          features: [
            'CRUD operations',
            'Search and filtering',
            'Pagination',
            'Statistics'
          ]
        },
        departments: {
          base: '/api/departments',
          methods: ['GET', 'POST', 'PUT', 'DELETE'],
          features: [
            'CRUD operations',
            'Employee management',
            'Statistics',
            'Hierarchy management'
          ]
        },
        attendance: {
          base: '/api/attendance',
          methods: ['GET', 'POST', 'PUT', 'DELETE'],
          features: [
            'Clock in/out',
            'Break management',
            'Monthly reports',
            'Statistics'
          ]
        },
        health: '/api/health',
        info: '/api/info'
      }
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * Default route handler for undefined endpoints
 */
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'API endpoint not found',
    message: `The endpoint ${req.method} ${req.originalUrl} does not exist`,
    availableEndpoints: '/api/info',
    timestamp: new Date().toISOString()
  });
});

export default router;