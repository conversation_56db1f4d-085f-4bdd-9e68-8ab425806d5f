/**
 * ============================================================================
 * EMPLOYEE MANAGEMENT ROUTES
 * ============================================================================
 *
 * This module defines all HTTP routes for employee operations including
 * CRUD operations, advanced search, filtering, statistics, and reporting.
 *
 * @fileoverview Employee management and operations routes
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 1.0.0
 * @created 2024-01-01
 * @updated 2024-07-02
 *
 * @description
 * Employee routes handle:
 * - Employee CRUD operations (Create, Read, Update, Delete)
 * - Advanced search and filtering capabilities
 * - Employee statistics and analytics
 * - Pagination and sorting
 * - Department-based filtering
 * - Status management (active, inactive, terminated)
 *
 * Security:
 * - All routes require authentication
 * - Role-based access control (RBAC)
 * - Input validation and sanitization
 * - Soft delete for data integrity
 */

import express from 'express';
import {
  getAllEmployees,
  getEmployeeById,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getEmployeeStats,
  searchEmployees
} from '../controllers/employeeController.js';

const router = express.Router();

/**
 * ============================================================================
 * EMPLOYEE LISTING & SEARCH ROUTES
 * ============================================================================
 */

/**
 * @route   GET /api/employees
 * @desc    Get paginated list of employees with filtering and sorting options
 * @access  Private (requires authentication)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @query {number} [page=1] - Page number for pagination (min: 1)
 * @query {number} [limit=10] - Number of items per page (min: 1, max: 100)
 * @query {string} [search] - Search term for name, email, or employee ID
 * @query {string} [department] - Filter by department ID
 * @query {string} [status] - Filter by status (active, inactive, terminated)
 * @query {string} [role] - Filter by role (employee, manager, hr, admin)
 * @query {string} [position] - Filter by job position/title
 * @query {string} [sortBy=createdAt] - Sort field (firstName, lastName, email, hireDate, etc.)
 * @query {string} [sortOrder=desc] - Sort direction (asc, desc)
 * @query {string} [dateFrom] - Filter by hire date from (ISO date string)
 * @query {string} [dateTo] - Filter by hire date to (ISO date string)
 *
 * @returns {Object} 200 - Employees retrieved successfully
 * @returns {Array} returns.data.employees - Array of employee objects
 * @returns {Object} returns.data.pagination - Pagination metadata
 * @returns {number} returns.data.pagination.currentPage - Current page number
 * @returns {number} returns.data.pagination.totalPages - Total number of pages
 * @returns {number} returns.data.pagination.totalItems - Total number of employees
 * @returns {number} returns.data.pagination.itemsPerPage - Items per page
 * @returns {Object} 400 - Invalid query parameters
 * @returns {Object} 401 - Authentication required
 *
 * @example
 * GET /api/employees?page=1&limit=20&department=60d5ecb74b24a1234567890b&status=active&sortBy=firstName&sortOrder=asc
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Employees retrieved successfully",
 *   "data": {
 *     "employees": [
 *       {
 *         "_id": "60d5ecb74b24a1234567890a",
 *         "firstName": "John",
 *         "lastName": "Doe",
 *         "email": "<EMAIL>",
 *         "employeeId": "EMP001",
 *         "position": "Software Engineer",
 *         "department": {
 *           "name": "Engineering",
 *           "code": "ENG"
 *         },
 *         "status": "active",
 *         "hireDate": "2024-01-15T00:00:00.000Z"
 *       }
 *     ],
 *     "pagination": {
 *       "currentPage": 1,
 *       "totalPages": 5,
 *       "totalItems": 87,
 *       "itemsPerPage": 20
 *     }
 *   }
 * }
 */
router.get('/', getAllEmployees);

/**
 * @route   GET /api/employees/stats
 * @desc    Get comprehensive employee statistics and analytics
 * @access  Private (requires authentication, HR/Admin preferred)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @returns {Object} 200 - Statistics retrieved successfully
 * @returns {Object} returns.data.overview - General statistics overview
 * @returns {number} returns.data.overview.totalEmployees - Total number of employees
 * @returns {number} returns.data.overview.activeEmployees - Number of active employees
 * @returns {number} returns.data.overview.newHiresThisMonth - New hires in current month
 * @returns {Array} returns.data.departmentBreakdown - Employees by department
 * @returns {Array} returns.data.roleDistribution - Employees by role
 * @returns {Array} returns.data.statusBreakdown - Employees by status
 * @returns {Object} returns.data.trends - Hiring and termination trends
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 *
 * @example
 * GET /api/employees/stats
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
router.get('/stats', getEmployeeStats);

/**
 * @route   GET /api/employees/search
 * @desc    Advanced employee search with multiple criteria and filters
 * @access  Private (requires authentication)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @query {string} [q] - General search query (searches name, email, employee ID)
 * @query {string} [department] - Filter by department ID or name
 * @query {string} [position] - Filter by job position/title (partial match)
 * @query {string} [status] - Filter by employment status
 * @query {string} [role] - Filter by user role
 * @query {string} [skills] - Filter by skills (comma-separated)
 * @query {string} [dateFrom] - Hire date from (ISO date string)
 * @query {string} [dateTo] - Hire date to (ISO date string)
 * @query {number} [salaryMin] - Minimum salary filter
 * @query {number} [salaryMax] - Maximum salary filter
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of results per page
 * @query {string} [sortBy=relevance] - Sort field
 * @query {string} [sortOrder=desc] - Sort direction
 *
 * @returns {Object} 200 - Search results retrieved successfully
 * @returns {Array} returns.data.results - Array of matching employees
 * @returns {Object} returns.data.searchMetadata - Search metadata and filters applied
 * @returns {Object} returns.data.pagination - Pagination information
 * @returns {Object} 400 - Invalid search parameters
 * @returns {Object} 401 - Authentication required
 *
 * @example
 * GET /api/employees/search?q=john&department=engineering&position=engineer&status=active
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */
router.get('/search', searchEmployees);

/**
 * @route   GET /api/employees/:id
 * @desc    Get detailed information for a specific employee by ID
 * @access  Private (requires authentication)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Employee ID (MongoDB ObjectId, required)
 *
 * @returns {Object} 200 - Employee retrieved successfully
 * @returns {Object} returns.data - Complete employee information with department details
 * @returns {Object} 400 - Invalid employee ID format
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 404 - Employee not found
 *
 * @example
 * GET /api/employees/60d5ecb74b24a1234567890a
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Employee retrieved successfully",
 *   "data": {
 *     "_id": "60d5ecb74b24a1234567890a",
 *     "firstName": "John",
 *     "lastName": "Doe",
 *     "email": "<EMAIL>",
 *     "employeeId": "EMP001",
 *     "position": "Software Engineer",
 *     "department": {
 *       "_id": "60d5ecb74b24a1234567890b",
 *       "name": "Engineering",
 *       "code": "ENG"
 *     },
 *     "role": "employee",
 *     "status": "active",
 *     "hireDate": "2024-01-15T00:00:00.000Z",
 *     "salary": 75000,
 *     "phone": "******-0123",
 *     "address": {...},
 *     "emergencyContact": {...},
 *     "skills": ["JavaScript", "React", "Node.js"]
 *   }
 * }
 */
router.get('/:id', getEmployeeById);

/**
 * ============================================================================
 * EMPLOYEE CRUD OPERATIONS
 * ============================================================================
 */

/**
 * @route   POST /api/employees
 * @desc    Create a new employee record in the system
 * @access  Private (requires authentication, HR/Admin role preferred)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @body {Object} employeeData - New employee information
 * @body {string} employeeData.firstName - First name (required, 2-50 chars)
 * @body {string} employeeData.lastName - Last name (required, 2-50 chars)
 * @body {string} employeeData.email - Email address (required, unique, valid format)
 * @body {string} employeeData.password - Initial password (required, min 8 chars)
 * @body {string} [employeeData.employeeId] - Unique employee ID (auto-generated if not provided)
 * @body {string} employeeData.position - Job position/title (required)
 * @body {string} employeeData.department - Department ID (required, must exist)
 * @body {string} [employeeData.role=employee] - User role (employee, manager, hr, admin)
 * @body {string} [employeeData.status=active] - Employment status (active, inactive)
 * @body {Date} [employeeData.hireDate] - Hire date (defaults to current date)
 * @body {number} [employeeData.salary] - Annual salary (positive number)
 * @body {string} [employeeData.phone] - Phone number (valid format)
 * @body {Object} [employeeData.address] - Address information
 * @body {Object} [employeeData.emergencyContact] - Emergency contact details
 * @body {Array<string>} [employeeData.skills] - Array of skills/competencies
 *
 * @returns {Object} 201 - Employee created successfully
 * @returns {Object} returns.data - Created employee information
 * @returns {Object} 400 - Validation error or invalid data
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 * @returns {Object} 409 - Email or employee ID already exists
 *
 * @example
 * POST /api/employees
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Content-Type: application/json
 *
 * {
 *   "firstName": "Jane",
 *   "lastName": "Smith",
 *   "email": "<EMAIL>",
 *   "password": "SecurePass123!",
 *   "position": "Senior Developer",
 *   "department": "60d5ecb74b24a1234567890b",
 *   "salary": 85000,
 *   "phone": "******-0123"
 * }
 */
router.post('/', createEmployee);

/**
 * @route   PUT /api/employees/:id
 * @desc    Update an existing employee's information
 * @access  Private (requires authentication, HR/Admin or self-update for limited fields)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Employee ID (MongoDB ObjectId, required)
 *
 * @body {Object} updateData - Employee update data (all fields optional)
 * @body {string} [updateData.firstName] - First name (2-50 chars)
 * @body {string} [updateData.lastName] - Last name (2-50 chars)
 * @body {string} [updateData.email] - Email address (unique, valid format)
 * @body {string} [updateData.position] - Job position/title
 * @body {string} [updateData.department] - Department ID (must exist)
 * @body {string} [updateData.role] - User role (HR/Admin only)
 * @body {string} [updateData.status] - Employment status (HR/Admin only)
 * @body {number} [updateData.salary] - Annual salary (HR/Admin only)
 * @body {string} [updateData.phone] - Phone number
 * @body {Object} [updateData.address] - Address information
 * @body {Object} [updateData.emergencyContact] - Emergency contact details
 * @body {Array<string>} [updateData.skills] - Skills/competencies
 *
 * @returns {Object} 200 - Employee updated successfully
 * @returns {Object} returns.data - Updated employee information
 * @returns {Object} 400 - Validation error or invalid data
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions
 * @returns {Object} 404 - Employee not found
 * @returns {Object} 409 - Email already exists
 *
 * @example
 * PUT /api/employees/60d5ecb74b24a1234567890a
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * Content-Type: application/json
 *
 * {
 *   "position": "Lead Developer",
 *   "salary": 95000,
 *   "skills": ["JavaScript", "React", "Node.js", "TypeScript"]
 * }
 */
router.put('/:id', updateEmployee);

/**
 * @route   DELETE /api/employees/:id
 * @desc    Delete an employee record (soft delete - sets status to terminated)
 * @access  Private (requires authentication, HR/Admin role required)
 * @headers {string} Authorization - Bearer JWT token (required)
 *
 * @param {string} id - Employee ID (MongoDB ObjectId, required)
 *
 * @returns {Object} 200 - Employee deleted successfully
 * @returns {Object} returns.data - Confirmation of deletion with final employee state
 * @returns {Object} 400 - Invalid employee ID format
 * @returns {Object} 401 - Authentication required
 * @returns {Object} 403 - Insufficient permissions (HR/Admin required)
 * @returns {Object} 404 - Employee not found
 * @returns {Object} 409 - Cannot delete employee with active dependencies
 *
 * @note This performs a soft delete by setting the employee status to 'terminated'
 *       and preserving all historical data for compliance and reporting purposes.
 *
 * @example
 * DELETE /api/employees/60d5ecb74b24a1234567890a
 * Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Employee deleted successfully",
 *   "data": {
 *     "employeeId": "EMP001",
 *     "name": "John Doe",
 *     "status": "terminated",
 *     "terminationDate": "2024-07-02T10:30:00.000Z"
 *   }
 * }
 */
router.delete('/:id', deleteEmployee);

/**
 * ============================================================================
 * EXPORT ROUTER
 * ============================================================================
 */

export default router;