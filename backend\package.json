{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "mongoose": "^8.16.1", "nodemon": "^3.1.10"}}