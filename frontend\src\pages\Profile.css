.profile-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.profile-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profile-avatar {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 auto 1rem;
  text-transform: uppercase;
}

.profile-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 0.25rem 0;
}

.profile-role {
  font-size: 0.875rem;
  color: var(--color-primary);
  font-weight: 500;
  margin: 0 0 0.125rem 0;
}

.profile-department {
  font-size: 0.8125rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.profile-stats {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: var(--shadow-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--color-border-light);
}

.stat-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.stat-item:first-child {
  padding-top: 0;
}

.stat-label {
  font-size: 0.8125rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 0.8125rem;
  color: var(--color-text-primary);
  font-weight: 600;
}

.status-active {
  color: var(--color-success) !important;
}

.profile-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border-light);
}

.section-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.edit-actions {
  display: flex;
  gap: 0.5rem;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-display {
  padding: 0.75rem 0;
  color: var(--color-text-primary);
  font-size: 0.875rem;
  min-height: 1.25rem;
  border-bottom: 1px solid transparent;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: 250px 1fr;
    gap: 1.5rem;
  }
  
  .profile-avatar {
    padding: 1.25rem;
  }
  
  .avatar-placeholder {
    width: 70px;
    height: 70px;
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .profile-sidebar {
    order: 2;
  }
  
  .profile-main {
    order: 1;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .edit-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .profile-section {
    padding: 1rem;
  }
  
  .profile-avatar {
    padding: 1rem;
  }
  
  .avatar-placeholder {
    width: 60px;
    height: 60px;
    font-size: 1rem;
  }
  
  .profile-name {
    font-size: 1.125rem;
  }
  
  .edit-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .edit-actions .btn {
    width: 100%;
    justify-content: center;
  }
}
