.home {
  min-height: calc(100vh - 70px);
  background: var(--color-bg-secondary);
}

.hero-section {
  padding: 4rem 2rem;
  text-align: center;
  background: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border-light);
}

.hero-content {
  max-width: 700px;
  margin: 0 auto;
}

.hero-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
  letter-spacing: -0.025em;
}

.hero-subtitle {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
  line-height: 1.6;
  font-weight: 400;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.features-section {
  padding: 4rem 2rem;
  background: var(--color-bg-secondary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 3rem;
  color: var(--color-text-primary);
  font-weight: 600;
  letter-spacing: -0.025em;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: var(--color-bg-primary);
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  transition: all 0.2s ease;
}

.feature-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-border-medium);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: var(--color-text-primary);
  font-weight: 600;
}

.feature-card p {
  color: var(--color-text-secondary);
  line-height: 1.6;
  font-size: 0.875rem;
}

.stats-section {
  padding: 4rem 2rem;
  background: var(--color-bg-primary);
  border-top: 1px solid var(--color-border-light);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  text-align: center;
}

.stat-item {
  padding: 2rem 1rem;
  border-radius: 8px;
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-light);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--color-primary);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 1.5rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .features-section {
    padding: 3rem 1.5rem;
  }

  .stats-section {
    padding: 3rem 1.5rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-item {
    padding: 1.5rem 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.75rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
