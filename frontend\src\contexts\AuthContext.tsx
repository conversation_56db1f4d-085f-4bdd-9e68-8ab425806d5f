import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  department: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in (from localStorage)
    const savedUser = localStorage.getItem('hrms_user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('hrms_user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);

    try {
      const response = await fetch('http://localhost:5000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        const { token, user: userData } = data.data;

        // Store token and user data
        localStorage.setItem('hrms_token', token);
        localStorage.setItem('hrms_user', JSON.stringify(userData));

        // Update user state
        setUser({
          id: userData._id,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: userData.role,
          department: userData.department?.name || userData.department
        });

        return true;
      } else {
        console.error('Login failed:', data.error);
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      // Fallback to demo users if backend is not available
      return await loginWithDemoUsers(email, password);
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithDemoUsers = async (email: string, password: string): Promise<boolean> => {
    // Demo users for development when backend is not available
    const demoUsers: User[] = [
      {
        id: 'emp-001',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'Employee',
        department: 'Engineering'
      },
      {
        id: 'admin-001',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'Admin',
        department: 'Human Resources'
      },
      {
        id: 'emp-002',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'Manager',
        department: 'Product'
      },
      {
        id: 'emp-003',
        email: '<EMAIL>',
        firstName: 'Mike',
        lastName: 'Johnson',
        role: 'HR Manager',
        department: 'Human Resources'
      },
      {
        id: 'emp-004',
        email: '<EMAIL>',
        firstName: 'Sarah',
        lastName: 'Wilson',
        role: 'Marketing Specialist',
        department: 'Marketing'
      }
    ];

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Demo password is 'password123' for all users
    if (password === 'password123') {
      const foundUser = demoUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
      if (foundUser) {
        setUser(foundUser);
        localStorage.setItem('hrms_user', JSON.stringify(foundUser));
        return true;
      }
    }

    return false;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('hrms_user');
    localStorage.removeItem('hrms_token');
  };

  const value: AuthContextType = {
    user,
    login,
    logout,
    isLoading,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
