.navigation {
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  padding: 0;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--color-border-light);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  height: 70px;
}

.nav-brand {
  font-size: 1.5rem;
  font-weight: 700;
}

.nav-brand-link {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.nav-brand-link:hover {
  color: var(--color-primary-dark);
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
}

.nav-item {
  margin: 0;
}

.nav-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: 0.875rem;
  position: relative;
}

.nav-link:hover {
  color: var(--color-text-primary);
  background: var(--color-gray-50);
}

.nav-link.active {
  color: var(--color-primary);
  background: rgba(79, 70, 229, 0.1);
}

.nav-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  text-align: right;
  color: var(--color-text-primary);
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.user-role {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  font-weight: 400;
  margin-top: 2px;
}

.logout-btn {
  background: var(--color-bg-primary);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border-medium);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: inherit;
}

.logout-btn:hover {
  color: var(--color-text-primary);
  border-color: var(--color-border-dark);
  background: var(--color-gray-50);
}

.logout-btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

@media (max-width: 1024px) {
  .nav-container {
    padding: 0 1.5rem;
  }

  .nav-menu {
    gap: 0.25rem;
  }

  .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }

  .user-info {
    font-size: 0.8125rem;
  }

  .logout-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }
}

@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    height: auto;
    padding: 1rem;
    gap: 1rem;
  }

  .nav-menu {
    flex-wrap: wrap;
    gap: 0.25rem;
    justify-content: center;
  }

  .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }

  .nav-user {
    gap: 0.75rem;
    flex-direction: column;
    text-align: center;
  }

  .user-info {
    text-align: center;
    font-size: 0.8125rem;
  }

  .logout-btn {
    font-size: 0.8125rem;
    padding: 0.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0.75rem;
  }

  .nav-brand {
    font-size: 1.25rem;
  }

  .nav-menu {
    gap: 0.125rem;
  }

  .nav-link {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .user-name {
    font-size: 0.75rem;
  }

  .user-role {
    font-size: 0.6875rem;
  }
}
